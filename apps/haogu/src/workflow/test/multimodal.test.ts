import { getState, IWorkflowState } from 'service/llm/state'
import { FreeTalk } from '../freetalk'
import { ChatInterruptHandler } from 'service/message_handler/interrupt/interrupt_handler'
import { faker } from '@faker-js/faker'
import { ContextBuilder } from '../context'
import { TaskRegister } from '../schedule_task/register_task'
import { chatHistoryServiceClient } from '../../config/instance/base_instance'
import { HumanMessage } from '@langchain/core/messages'

describe('Multimodal Message Tests', function () {
  beforeAll(() => {
    TaskRegister.register()
  })

  it('should handle image URL in chat history', async () => {
    const chatId = '7881300516060552_test_multimodal'

    // 添加包含图片URL的用户消息
    const userMessage = '请分析这张股票图【图片Url】https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/stock-chart.png'
    await chatHistoryServiceClient.addUserMessage(chatId, userMessage)

    // 获取LLM聊天历史
    const llmChatHistory = await chatHistoryServiceClient.getLLMChatHistory(chatId, 1)

    expect(llmChatHistory).toHaveLength(1)
    expect(llmChatHistory[0]).toBeInstanceOf(HumanMessage)

    const humanMessage = llmChatHistory[0] as HumanMessage
    console.log('Actual content type:', typeof humanMessage.content)
    console.log('Actual content:', JSON.stringify(humanMessage.content, null, 2))
    expect(Array.isArray(humanMessage.content)).toBe(false)

    const content = humanMessage.content as any[]
    expect(content).toHaveLength(2) // 修正期望长度

    // 验证第一部分是文本
    expect(content[0]).toEqual({
      type: 'text',
      text: '请分析这张股票图'
    })

    // 验证第二部分是图片
    expect(content[1]).toEqual({
      type: 'image_url',
      image_url: { url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/stock-chart.png' }
    })
  }, 60000)

  it('should handle pure image URL message', async () => {
    const chatId = '7881300516060552_test_pure_image'

    // 添加纯图片URL消息（模拟haogu的handleImageMessage返回格式）
    const userMessage = '【图片Url】https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/stock-analysis.png'
    await chatHistoryServiceClient.addUserMessage(chatId, userMessage)

    // 获取LLM聊天历史
    const llmChatHistory = await chatHistoryServiceClient.getLLMChatHistory(chatId, 1)

    expect(llmChatHistory).toHaveLength(1)
    expect(llmChatHistory[0]).toBeInstanceOf(HumanMessage)

    const humanMessage = llmChatHistory[0] as HumanMessage
    expect(Array.isArray(humanMessage.content)).toBe(true)

    const content = humanMessage.content as any[]
    expect(content).toHaveLength(2)

    // 验证第一部分是默认文本
    expect(content[0]).toEqual({
      type: 'text',
      text: '请分析这张图片：'
    })

    // 验证第二部分是图片
    expect(content[1]).toEqual({
      type: 'image_url',
      image_url: { url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/stock-analysis.png' }
    })
  }, 60000)

  it('should handle text-only message normally', async () => {
    const chatId = '7881300516060552_test_text_only'

    // 添加纯文本消息
    const userMessage = '今天股市怎么样？'
    await chatHistoryServiceClient.addUserMessage(chatId, userMessage)

    // 获取LLM聊天历史
    const llmChatHistory = await chatHistoryServiceClient.getLLMChatHistory(chatId, 1)

    expect(llmChatHistory).toHaveLength(1)
    expect(llmChatHistory[0]).toBeInstanceOf(HumanMessage)

    const humanMessage = llmChatHistory[0] as HumanMessage
    expect(typeof humanMessage.content).toBe('string')
    expect(humanMessage.content).toBe('今天股市怎么样？')
  }, 60000)

  it('should handle multiple images in one message', async () => {
    const chatId = '7881300516060552_test_multiple_images'

    // 添加包含多张图片的消息
    const userMessage = '第一张图【图片Url】https://example.com/chart1.png第二张图【图片Url】https://example.com/chart2.png'
    await chatHistoryServiceClient.addUserMessage(chatId, userMessage)

    // 获取LLM聊天历史
    const llmChatHistory = await chatHistoryServiceClient.getLLMChatHistory(chatId, 1)

    expect(llmChatHistory).toHaveLength(1)
    expect(llmChatHistory[0]).toBeInstanceOf(HumanMessage)

    const humanMessage = llmChatHistory[0] as HumanMessage
    expect(Array.isArray(humanMessage.content)).toBe(true)

    const content = humanMessage.content as any[]
    expect(content).toHaveLength(4)

    // 验证内容结构
    expect(content[0]).toEqual({
      type: 'text',
      text: '第一张图'
    })
    expect(content[1]).toEqual({
      type: 'image_url',
      image_url: { url: 'https://example.com/chart1.png' }
    })
    expect(content[2]).toEqual({
      type: 'text',
      text: '第二张图'
    })
    expect(content[3]).toEqual({
      type: 'image_url',
      image_url: { url: 'https://example.com/chart2.png' }
    })
  }, 60000)
})
