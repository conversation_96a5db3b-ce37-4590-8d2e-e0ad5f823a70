import logger from 'model/logger/logger'
import { AsyncLock } from 'model/lock/lock'
import { getState, IWorkflowState } from 'service/llm/state'
import { Node } from './node'
import { NodeMap, Router } from './router'
import { BaseWorkFlow } from 'service/workflow/workflow'
import { InterruptError } from 'service/message_handler/interrupt/interrupt_handler'
import { VisualizedSopTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { Config } from 'config'
import { GroupNotification } from 'service/group_notification/group_notification'
import { override } from '../config/override'
import { HumanTransferType } from '../config/manifest'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../config/instance/base_instance'
import { extractUserSlots, humanTransfer, memoryStoreClient } from '../config/instance/instance'
import { commonMessageSender, wecomMessageSender } from '../config/instance/send_message_instance'

export class Workflow extends BaseWorkFlow {
  /**
     * 对话流程
     * @param chat_id
     * @param user_id
     * @param userMessage
     */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    try {
      userMessage = this.transferWechatEmoji(userMessage)
      await chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
      if (userMessage === '【表情】') {
        return
      } else if (userMessage === '【语音/视频通话】') {
        await humanTransfer.transfer(chat_id, user_id, HumanTransferType.VoiceOrVideoCall, 'onlyNotify')
        await commonMessageSender.sendText(chat_id, {
          text: '你好，我这边在忙不方便接听电话，你要是有什么事情可以直接发消息哈，我微信可以回复消息的',
          description: '客户发起语音/视频通话'
        })
        return
      } else if (userMessage.toLowerCase() === 'clear' && Config.isTestAccount()) {
        await this.resetChat(chat_id, user_id)
        return
      }

      const lock = new AsyncLock()

      await lock.acquire(chat_id, async () => { // 如果有新消息，当前回复会被丢弃
        const entryNode = (await chatStateStoreClient.get(chat_id)).nextStage
        const state = await getState(chat_id, user_id, userMessage) // 如果有新消息，在锁释放后，当前流程会中断
        await Workflow.run(entryNode as Node, state)
      }, { timeout: 2 * 60 * 1000 })
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          if (e instanceof Error) {
            // 将错误的堆栈信息打印到自定义 logger 中
            logger.error(`Error: ${e.message}\nStack Trace: ${e.stack}`)
          } else {
            // 如果 e 不是 Error 实例，捕获当前的堆栈信息
            const stack = new Error().stack
            logger.error(`消息回复失败: ${stack}`)
          }
          await humanTransfer.transfer(chat_id, user_id, HumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    }
  }

  private static async run (entryNode: Node, state: IWorkflowState) {
    let node = NodeMap[entryNode]
    logger.trace({ chat_id: state.chat_id }, `初始跳转节点: ${entryNode}`)
    if (!node) {
      logger.error(`[YuheFlow] node not found: ${entryNode}`)
      return
    }

    await this.preReply(state)

    // 根据客户消息自动转移
    const autoTransferNode = await Router.route(state)
    if (autoTransferNode === Node.DummyEnd) {
      return
    }

    if (autoTransferNode && autoTransferNode !== Node.Dummy) {
      logger.trace({ chat_id: state.chat_id }, `重定向到节点: ${autoTransferNode}`)

      node = NodeMap[autoTransferNode]
      if (!node) {
        logger.error(`[YuheFlow] auto transfer node not found: ${autoTransferNode}`)
        return
      }
    }

    const nextStage = await node.invoke(state)
    await chatStateStoreClient.update(state.chat_id, { nextStage })

    await this.postReply(state)
  }

  private static async postReply(state: IWorkflowState) {

  }

  public static async humanInvolveGroupNotify(contactName: string, courseNo: number | null, userMessage: string) {
    await GroupNotification.notify(`${contactName}（${courseNo}）客户AI未开启，请人工处理\n客户：${userMessage}`)
  }

  private static transferWechatEmoji(message: string) {
    const emojiRegex = /\[.*?\]/g
    const emojiMap:Record<string, string> = {
      '[微笑]': '😊',
      '[调皮]': '😝',
      '[合十]': '🙏',
      '[爱心]': '💗',
      '[玫瑰]': '🌹',
      '[捂脸]': '🤦',
      '[笑哭]': '😂',
      '[咖啡]': '☕️',
      '[抱拳]': '🙏',
      '[拥抱]': '🫂'
    }
    return message.replace(emojiRegex, (match) => {
      const emoji = emojiMap[match]
      return emoji || match
    })
  }

  private static async preReply(state: IWorkflowState) {
    // 异步提取 Memory, 客户槽位
    memoryStoreClient.pushRecentMemoryToVectorDB(state.chat_id, state.round_id, extractUserSlots)
  }

  private static async resetChat(chat_id: string, user_id: string) {
    await wecomMessageSender.sendById({ user_id, chat_id: chat_id, ai_msg: '聊天已重置' })
    await chatHistoryServiceClient.clearChatHistory(chat_id)
    await chatStateStoreClient.clear(chat_id)
    if (await chatDBClient.getById(chat_id)) {
      await chatDBClient.setHumanInvolvement(chat_id, false)
    }

    await VisualizedSopTasks.clearSop('haogu', chat_id)

    // clear memory
    await memoryStoreClient.clearMemory(chat_id)

    await override.sendWelcomeMessage(chat_id, user_id)
  }
}