import { LLM } from 'lib/ai/llm/llm_model'
import { IReasoningRagTool, RagToolExecuteParams, ReasoningRagTool } from './reasoning_rag_tool'
import { PromptTemplate } from '@langchain/core/prompts'


interface PlanStep {

  reasoning: string
    plan: string
  op: string
  input: RagToolExecuteParams
  fallbacks?: string[]
}


interface Plan {
  steps: PlanStep[]
}


export class RewooRag {


  public static async isNeedReasoning(chatHistory: string, ragContext: string, roundId: string) {
    const template = `# 辅助决策
你是一个辅助决策的助手，负责根据对话历史和当前检索到的上下文判断是否需要进行详细的推理和工具调用来回答客户的问题
在做出判断时：
- 关注核心意图的匹配度，不拘泥于具体表述
- 允许同义、近义、隐含含义和概念泛化（如时间、数量、条件）来进行匹配
- 如果上下文已能直接或通过简单推理覆盖客户的核心问题，则判定为 NO；只有在关键信息缺失时才判定为 YES

# 对话历史
${chatHistory}
检索到的上下文：${ragContext}

# 输出要求
请严格按照如下 JSON 格式输出
{{
  "reason": "说明判定依据",
  "verdict": "YES"（需要额外信息） 或 "NO"(不需要额外信息),
  "missing_points": "描述缺少的信息"
}}`

    const promptTemplate = PromptTemplate.fromTemplate(template)
    const prompt = await promptTemplate.format({ chatHistory, ragContext })
    const response = await LLM.predict(prompt, { meta:{ round_id:roundId }, responseJSON: true, model:'gpt-5-mini', temperature: 0 })

    const jsonRes = JSON.parse(response)
    const verifyRes = jsonRes.verdict

    return verifyRes === 'YES'

  }

  public static async search(chatHistory: string, strategy: string, chatId: string, roundId: string) {

    const plan = await RewooRag.generatePlan(chatHistory, strategy, await ReasoningRagTool.getTools(), chatId, roundId)
    const evidence = await RewooRag.executePlan(plan)
    return await RewooRag.solver(chatHistory, strategy, evidence, roundId)
  }

  private static async generatePlan(chatHistory: string, strategy: string, tools: IReasoningRagTool[], chatId: string, roundId: string): Promise<Plan> {
    // Compose a human friendly list of tool names for the prompt
    const toolList = tools
      .map((t) => `* ${t.name}: ${t.description}`)
      .join('\n')
    const plannerPrompt = `你是一个计划专家，需要根据回复策略与客户的聊天记录生成可执行计划。请按以下格式输出 JSON，不要写任何其他文字：

{{"steps": [{{"reasoning": "<简短阐述为什么需要此步骤>","plan":"<步骤描述>" "op": "<工具键名>", "input": "<传入工具的查询>"}}, ...]}}

请注意：
1. 将复杂问题拆解为若干个步骤，每个步骤只调用一个工具。
2. op 字段必须是可用工具名称。
3. input 字段是传给工具的字符串。

可用工具：
{toolList}

回复策略：
{strategy}

聊天记录：
{chatHistory}
`

    const promptTemplate = await PromptTemplate.fromTemplate(plannerPrompt).format({ toolList, strategy, chatHistory })

    const response = await LLM.predict(promptTemplate, {
      meta: { round_id: roundId },
      responseJSON: true,
    })
    // The LLM returns a JSON string; parse it into the Plan type.  In
    // case of malformed output, we fall back to an empty plan to
    // prevent runtime exceptions.
    try {
      return this.plannerResponseToPlan(response, chatId, roundId)
    } catch (err) {
      console.warn('Failed to parse planner response', err)
      return { steps: [] }
    }
  }


  private static async executePlan(plan: Plan) {
    let result = ''
    for (const step of plan.steps) {
      const tool = await ReasoningRagTool.getToolByKey(step.op)
      if (!tool) {
        continue
      }
      const toolResult = await tool.execute(step.input)
      result += `
Plan:${step.plan}
Evidence:${toolResult}`
    }

    return result
  }


  private static async solver(chatHistory: string, strategy: string, evidence: string, roundId: string) {
    // Compose a concise closer prompt using strategy, chat history and gathered evidence
    const solverPrompt = `请你作为知识整合器，根据以下信息生成一段简明的内部知识总结，供后续回答使用。不要输出客套话或最终的客户回复，只需输出总结内容。

回复策略：
${strategy}

聊天记录：
${chatHistory}

证据：
${evidence}

请输出一段简洁的知识总结：`

    return await LLM.predict(solverPrompt, {
      meta: { round_id: roundId },
    })
  }


  private static async plannerResponseToPlan(response: string, chatId: string, roundId: string) {
    const jsonData = JSON.parse(response)
    return {
      steps: jsonData.steps.map((step: any) => {
        const reasoning = step.reasoning
        const op = step.op
        const plan = step.plan
        const input = step.input

        return {
          reasoning,
          plan,
          op,
          input: {
            chatId,
            roundId,
            ...input
          },
        }
      })
    } as Plan
  }
}