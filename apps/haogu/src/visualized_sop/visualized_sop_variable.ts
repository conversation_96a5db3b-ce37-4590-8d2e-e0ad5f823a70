import { HandleActionOption } from 'service/visualized_sop/visualized_sop_processor'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string;opt:HandleActionOption})=> Promise<void>> = {
}

export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
}
