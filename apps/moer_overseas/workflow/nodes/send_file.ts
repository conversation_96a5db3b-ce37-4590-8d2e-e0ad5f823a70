import { trackInvoke, WorkFlowNode } from './base_node'
import { IWorkflowState } from 'service/llm/state'
import { Node } from './types'
import { DataService } from '../../helper/getter/get_data'
import logger from 'model/logger/logger'
import { PreCourseCompletionTask } from '../../client/handler/task/pre_course_completion'
import { LLM } from 'lib/ai/llm/llm_model'
import { AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { DateHelper } from 'lib/date/date'
import { XMLHelper } from 'lib/xml/xml'
import { ContextBuilder } from '../context'
import { ChatHistoryService } from 'service/chat_history/chat_history'
import { energyTestLink } from '../../service/global_data'
import { UserLanguage } from '../../helper/language/user_language_verify'
import { chatStateStoreClient, chatHistoryServiceClient } from '../../service/base_instance'
import { replyClient, yCloudMessageSender } from '../../service/instance'
import { IScheduleTime } from '../../helper/tool/creat_schedule_task'


enum FileType {
  NodeInvoke = 0,
  PreCourseLink = 1,
  EnergyTestLink = 2,
  PreCourseCompletionGift = 3,
  Class1Reward = 4,
  Class2Reward = 5,
  Class3Reward = 6,
  LiveClassReminder = 7,
  Class1RecordingLink = 8,
  LiveClassReminder2 = 9,
  Class2RecordingLink = 10,
  NoRecordingReason = 11,
  RecordingFeedback = 12,
  Class3RecordingLink = 13,
  ExtraClassNoRecording = 14,
  ExtraClassRecordingLink = 15,
  Class1MindMap = 16,
  Class2MindMap = 17,
  Class3MindMap = 18,
  Class4MindMap = 19,
  CourseSchedule = 20,
  PreCourseRewardAudio = 21,
  Class1PDF = 22,
  Class1Audio = 23,
  Class2RewardAudio = 24,
  Class3RewardAudio = 25,
  Class4Audio = 26,
}

export class SendFileNode extends WorkFlowNode {
  private static fileNameList = [
    '小课堂课程链接',
    '能量测评链接',
    '小讲堂入学礼',
    '周一完课礼',
    '周二完课礼',
    '周三完课礼',
    '周一回放',
    '周二回放',
    '周三回放',
    '周四回放',
    '周一思维导图',
    '周二思维导图',
    '周三思维导图',
    '周四思维导图',
    // '课程安排与课程表',
    '海浪减压冥想音频',
    '身心对照表',
    '沉浸式秒睡',
    'α波冥想音频',
    '红靴子冥想音频',
    '蓝鹰预演冥想音频',
  ]

  private static fileCase: { [key: string]: FileType } = {
    '其他资料': FileType.NodeInvoke,
    '小课堂课程链接': FileType.PreCourseLink,
    '能量测评链接': FileType.EnergyTestLink,
    '小讲堂入学礼': FileType.PreCourseCompletionGift,
    '周一完课礼': FileType.Class1Reward,
    '周二完课礼': FileType.Class2Reward,
    '周三完课礼': FileType.Class3Reward,
    '周一思维导图': FileType.Class1MindMap,
    '周二思维导图': FileType.Class2MindMap,
    '周三思维导图': FileType.Class3MindMap,
    '周四思维导图': FileType.Class4MindMap,
    // '课程安排与课程表': FileType.CourseSchedule,
    '海浪减压冥想音频': FileType.PreCourseRewardAudio,
    '身心对照表': FileType.Class1PDF,
    '沉浸式秒睡': FileType.Class1Audio,
    'α波冥想音频': FileType.Class2RewardAudio,
    '红靴子冥想音频': FileType.Class3RewardAudio,
    '蓝鹰预演冥想音频': FileType.Class4Audio,
  }

  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const fileName = await this.classifier(state)
    const entryNode = (await chatStateStoreClient.get(state.chat_id)).nextStage as Node
    const currentTime = await DataService.getCurrentTime(state.chat_id)

    logger.log(`is_course_week?: ${currentTime.is_course_week}`)

    const fileNames: string[] = fileName.includes('，') ? fileName.split('，') : [fileName]
    const recordingLink: string | null = null
    logger.log(`fileNames to be sending: ${fileNames}`)

    for (const name of fileNames) {
      logger.log(`fileName to be sending: ${name}`)
      const fileType = this.getFileType(name, currentTime)
      logger.log(`fileType to be sending: ${fileType}`)

      switch (fileType) {

        case FileType.PreCourseLink:
          await this.sendRecordingLink(state, 0)
          break

        case FileType.EnergyTestLink:
        {
          const languageOption = await UserLanguage.getLanguageSetting(state.chat_id)
          const context = await ContextBuilder.build({
            state: state,
            customerChatRounds: 1,
            talkStrategyPrompt: `根据上下文告知客户能量测评链接：${energyTestLink}\n${languageOption}`,
          })
          await replyClient.invoke({
            state,
            context: context,
            promptName: 'send_energy_test'
          })
          break
        }

        case FileType.PreCourseCompletionGift:
          await PreCourseCompletionTask.sendPreCourseCompleteGift(state.chat_id)
          break

        case FileType.Class1Reward:
          await this.sendClassReward(state.chat_id, 1)
          break

        case FileType.Class2Reward:
          await this.sendClassReward(state.chat_id, 2)
          break

        case FileType.Class3Reward:
          await this.sendClassReward(state.chat_id, 3)
          break

        case FileType.LiveClassReminder:
          await this.handlerLiveClassReminder(state)
          break

        case FileType.LiveClassReminder2:
          await this.handlerLiveClassReminder(state)
          break

        case FileType.Class1RecordingLink:
          await this.sendRecordingLink(state, 1)
          break

        case FileType.Class2RecordingLink:
          await this.sendRecordingLink(state, 2)
          break

        case FileType.Class3RecordingLink:
          await this.sendRecordingLink(state, 3)
          break

        case FileType.ExtraClassRecordingLink:
          await this.sendRecordingLink(state, 4)
          break

          // case FileType.CourseSchedule:
          //   await new GroupSend().sendMsg(state.user_id, state.chat_id, [pre_course_day.self_introduction_image], '课程表', true)
          //   break

        case FileType.Class1PDF:
          await this.sendClassReward(state.chat_id, 1)
          break

          // case FileType.PreCourseRewardAudio:
          //   await new GroupSend().sendMsg(state.user_id, state.chat_id, [pre_course_day.pre_course_complete_reward_mp3], '海浪减压冥想音频', true)
          //   break

          // case FileType.Class1Audio:
          //   await new GroupSend().sendMsg(state.user_id, state.chat_id, [course_day.day1_class_complete_reward_mp3], '沉浸式秒睡', true)
          //   break
          //
          // case FileType.Class2RewardAudio:
          //   await new GroupSend().sendMsg(state.user_id, state.chat_id, [course_day.day2_class_complete_reward_mp3], 'a波冥想音频', true)
          //   break
          //
          // case FileType.Class3RewardAudio:
          //   await new GroupSend().sendMsg(state.user_id, state.chat_id, [course_day.send_recording_day_4_complete_mp3], '红靴子冥想音频', true)
          //   break
          //
          // case FileType.Class4Audio:
          //   if (isPaid) {
          //     await new GroupSend().sendMsg(state.user_id, state.chat_id, [course_day.send_day_4_recording_mp3], '蓝鹰预演冥想音频', true)
          //   } else {
          //     await LLMNode.invoke({
          //       state,
          //       dynamicPrompt: '根据上下文告知客户“这是老师的额外加播课，而且是系统班核心内容，没有音频哦”，并顺势询问客户对于加入21天系统课的顾虑'
          //     })
          //   }
          //   break

      }
    }
    return (await chatStateStoreClient.get(state.chat_id)).nextStage as Node
  }

  private static getFileType(name: string, currentTime: IScheduleTime): FileType {
    if (!this.fileNameList.includes(name)) {
      return FileType.NodeInvoke
    }

    if (!name.includes('回放')) {
      return this.fileCase[name]
    }

    if (!currentTime.is_course_week) {
      return FileType.NodeInvoke
    }

    if (name.includes('周一')) {
      return DateHelper.isTimeBefore(currentTime.time, '21:45:00') && currentTime.day == 1
        ? FileType.LiveClassReminder
        : FileType.Class1RecordingLink
    }

    if (name.includes('周二')) {
      return currentTime.day < 2 || (DateHelper.isTimeBefore(currentTime.time, '21:10:00') && currentTime.day == 2)
        ? FileType.LiveClassReminder2
        : FileType.Class2RecordingLink
    }

    if (name.includes('周三')) {
      if (currentTime.day < 3 || (DateHelper.isTimeBefore(currentTime.time, '20:00:00') && currentTime.day == 3)) {
        return FileType.NoRecordingReason
      }
      return currentTime.day == 3
        ? FileType.RecordingFeedback
        : FileType.Class3RecordingLink
    }

    if (name.includes('周四')) {
      return currentTime.day < 5
        ? FileType.ExtraClassNoRecording
        : FileType.ExtraClassRecordingLink
    }

    return FileType.NodeInvoke
  }

  private static async classifier(state: IWorkflowState) {
    const chatHistory = await chatHistoryServiceClient.getLLMChatHistory(state.chat_id, 2)
    const formattedChatHistory = ChatHistoryService.formatHistoryHelper(chatHistory)

    const fileNameListStr = this.fileNameList.join('，')
    const res = await LLM.predictMessage([
      new SystemMessage(`请根据当前时间和近两轮与客户交互中的信息（如“课程安排”“完课礼”“回放”等），判断客户需要的资料，并从以下列表中选择对应的资料输出：【${fileNameListStr}】，如果没有找到对应资料，就输出【其他资料】

要求：
请主要关注客户的需求，而不是麦子老师说的内容，历史已经发送过的资料不要重复发送
请不要胡乱判断资料，找不到对应资料，或者找到资料了不满足时间等条件，都输出【其他资料】

模糊匹配：
身体健康表（身心对照表）
α波纯音乐/音乐（α波冥想音频）
路易丝·海（身心对照表）
冥想练习指南（小讲堂入学礼）
测评表，频率表（能量测评链接）
五天的回放（周一回放，周二回放，周三回放，周四回放）

课程信息：
周一之前：小讲堂，讲述后续3天课程主要内容，普及冥想的作用，带领大家请体验下海浪减压的冥想
周一课程：第一课，20:00直播，情绪减压，解析情绪和睡眠问题的成因和处理方式，带练对情绪和睡眠非常有帮助的《沉浸式秒睡》
周二课程：第二课，20:00直播，财富唤醒，聚焦财富问题，通过《财富果园》的冥想带练，帮助大家扫除对于物质、财富的内心阻碍
周三课程：第三课，20:00直播，红靴子是老师最重磅的课程，老师会带练《红靴子飞跃》冥想，主要聚焦于大家专注力、内在能量的提升
周四课程：第四课，20:00直播，加播课，唐宁老师会传授只有线下才会开设的《蓝鹰预演》，帮助我们预演未来，同时还会学习无限数息法延长呼吸

输出格式：
1. 先将分析过程输出到 <reason></reason> 标签中，例如：<reason>因当前时间为周二22:20:00，客户在对话中表示要奖励，应该发送【周二完课礼】</reason>
2. 仅将资料内容输出到 <answer></answer> 标签中，例如：<answer>周二完课礼</answer>
请严格按照上述格式输出结果。`),
      new HumanMessage(`当前时间：2024-11-19 周二 14:15:04
对话记录：
客户: 嗯嗯[愉快]
谢谢麦子老师[玫瑰]
麦子老师: 😌不客气，继续享受你的冥想旅程哦！
麦子老师: 如果有什么想分享的，随时可以告诉我
客户: 期待完课奖励哦[调皮]`),
      new AIMessage('<reason>因当前时间为周二14:15:04，还没到周二课程的上课时间20:00，属于周一课后，客户在对话中表示期待完课奖励，应该发送【周一完课礼】</reason> <answer>周一完课礼</answer>'),
      new HumanMessage(`当前时间：2025-01-08 周三 11:20:26
对话记录：
客户: 收到谢谢
麦子老师: 😊
客户: 【图片】这是墨尔冥想课程的奖励页面，显示客户完成第二课后获得了《α波纯音乐：减压放松》。这类音乐有助于放松身心、减少干扰，提升专注力，非常适合在冥想或学习时使用。
麦子老师好，请问这个是在您这儿领取吗`),
      new AIMessage('<reason>因当前时间为周三11:20:26，客户提到领取完课奖励《α波纯音乐：减压放松》，根据模糊匹配规则，应该发送【α波冥想音频】。</reason> <answer>α波冥想音频</answer>'),
      new HumanMessage(`当前时间：2025-01-09 周四 15:16:00
对话记录：
客户: 好的
麦子老师: 嗯嗯
麦子老师: 下午工作累了，可以休息几分钟，和老师练习5MIN 红靴子，100%回血！
客户: 第一节课后 分析 是哪里的卡点 的那个奖励 就是唐李老师在视频里面说的那个`),
      new AIMessage('<reason>因当前时间为周四15:16:00，客户提到“第一节课后 分析 是哪里的卡点 的那个奖励”，意思是想要第一课的完课奖励，因此应输出【周一完课礼】。</reason> <answer>周一完课礼</answer>'),
      new HumanMessage(`当前时间：${DateHelper.getFormattedDate(new Date(), true)}
对话记录：\n${formattedChatHistory}`),
    ], { meta: { chat_id: state.chat_id, promptName: 'sendfile_category', round_id: state.round_id } })
    const fileName = XMLHelper.extractContent(res, 'answer')
    if (!fileName) {
      return '其他资料'
    }
    return fileName
  }


  private static async sendClassReward(chatId: string, day: number) {

    const classRewardList = await this.getClassRewardList(chatId)

    switch (day) {
      case 1:
        await yCloudMessageSender.sendById({
          chat_id: chatId,
          ai_msg: '[第一节课完课礼]',
          type: 'document',
          send_msg: {
            link: classRewardList[0].url,
            filename: classRewardList[0].name
          }
        })
        break
      case 2:
        await yCloudMessageSender.sendById({
          chat_id: chatId,
          ai_msg: '[第二节课完课礼]',
          type: 'document',
          send_msg: {
            link: classRewardList[1].url,
            filename: classRewardList[1].name
          }
        })
        break
      case 3:
        await yCloudMessageSender.sendById({
          chat_id: chatId,
          ai_msg: '[第三节课完课礼]',
          type: 'document',
          send_msg: {
            link: classRewardList[2].url,
            filename: classRewardList[2].name
          }
        })
        break
    }
  }

  private static async sendRecordingLink(state: IWorkflowState, day: number) {
    const languageOption = await UserLanguage.getLanguageSetting(state.chat_id)
    const recordLink = await DataService.getCourseLink(day, state.chat_id, true)
    const context = await ContextBuilder.build({
      state: state,
      customerChatRounds: 1,
      talkStrategyPrompt: `根据上下文告知客户链接：${recordLink}\n${languageOption}`
    })
    await replyClient.invoke({
      state,
      context: context,
      promptName: 'send_record_link'
    })
  }

  private static async handlerLiveClassReminder(state: IWorkflowState) {
    const languageOption = await UserLanguage.getLanguageSetting(state.chat_id)
    const context = await ContextBuilder.build({
      state: state,
      customerChatRounds: 1,
      talkStrategyPrompt: `根据上下文告知客户：有回放的，但是还是来参加直播课效果更好
${languageOption}`
    })
    await replyClient.invoke({
      state,
      context: context,
      promptName: 'live_class_reminder'
    })
  }

  private static async getClassRewardList(chatId: string) {

    const classReward_CN = [
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/cn/%E7%AC%AC%E4%B8%80%E8%AF%BE%E5%A5%96%E5%8A%B1%EF%BC%88cn%EF%BC%89.pdf',
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/cn/%E6%B7%B1%E5%BA%A6%CE%B1%E6%B3%A2%E5%86%A5%E6%83%B3%E9%9F%B3%E4%B9%90.mp3',
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/cn/%E6%B7%B1%E5%BA%A6%CE%B1%E6%B3%A2%E5%86%A5%E6%83%B3%E9%9F%B3%E4%B9%90.mp3'
    ]

    const classReward_EN = [
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/en/%E7%AC%AC%E4%B8%80%E8%AF%BE%E5%A5%96%E5%8A%B1(en).pdf',
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/en/%E6%B7%B1%E5%BA%A6%CE%B1%E6%B3%A2%E5%86%A5%E6%83%B3%E9%9F%B3%E4%B9%90.mp3',
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/en/%E7%BA%A2%E9%9D%B4%E5%AD%90%E8%8B%B1%E6%96%87%E7%89%88.MP3'
    ]

    const classRewardName_CN = ['身心對照表', '深度α波冥想音樂', '紅靴子冥想音頻']
    const classRewardName_EN = ['Mind-Body Cross-Reference Chart', 'Deep Alpha Wave Meditation Music', 'Red Boots Meditation Audio']

    const language = await UserLanguage.getLanguage(chatId)

    const urls = language === UserLanguage.Language_ZH ? classReward_CN :
      language === UserLanguage.Language_EN ? classReward_EN : classReward_EN

    const names = language === UserLanguage.Language_ZH ? classRewardName_CN :
      language === UserLanguage.Language_EN ? classRewardName_EN : classRewardName_EN

    return urls.map((url, index) => {
      return {
        name: names[index],
        url
      }
    })
  }
}